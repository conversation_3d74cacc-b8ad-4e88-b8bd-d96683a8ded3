<?php

namespace App\Controllers;

use App\Models\APPX_ApplicationInformationModel;
use App\Models\APPX_ApplicationExperiencesModel;
use App\Models\APPX_ApplicationEducationModel;
use App\Models\APPX_ApplicationFilesModel;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\PositionsGroupModel;
use App\Models\applicantsModel; // Keep if needed for applicant info display

class ApplicationPreScreeningController extends BaseController
{
    protected $applicationModel;
    protected $applicantsModel;
    protected $experienceModel;
    protected $educationModel;
    protected $filesModel;
    protected $exerciseModel;
    protected $positionModel;
    protected $positionGroupModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']); // Assuming application_helper exists
        $this->session = \Config\Services::session();
        $this->applicationModel = new APPX_ApplicationInformationModel();
        $this->applicantsModel = new applicantsModel();
        $this->experienceModel = new APPX_ApplicationExperiencesModel();
        $this->educationModel = new APPX_ApplicationEducationModel();
        $this->filesModel = new APPX_ApplicationFilesModel();
        $this->exerciseModel = new ExerciseModel();
        $this->positionModel = new PositionsModel();
        $this->positionGroupModel = new PositionsGroupModel();
    }

    /**
     * [GET] Display the list of applications pending pre-screening.
     * URI: /application_pre_screening
     */
    public function index()
    {
        // Get all applications where received_acknowledged is not empty and pre_screened is empty/null
        $applications = $this->applicationModel
            ->select('appx_application_information.*, applicants.fname, applicants.lname, positions.designation as position_name')
            ->join('applicants', 'applicants.applicant_id = appx_application_information.applicant_id', 'left')
            ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
            /* ->groupStart()
                ->where('appx_application_information.pre_screened IS NULL')
                ->orWhere('appx_application_information.pre_screened', '')
            ->groupEnd() */
            ->orderBy('appx_application_information.created_at', 'DESC') // Example ordering
            ->findAll();

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('application_pre_screening/application_pre_screening_list', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_pre_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        // Get application with related position and exercise data
        $application = $this->applicationModel
            ->select('
                appx_application_information.*,
                positions.designation,
                positions_groups.group_name,
                exercises.id as exercise_id,
                exercises.exercise_name,
                exercises.pre_screen_criteria
            ')
            ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id', 'left')
            ->join('exercises', 'exercises.id = positions_groups.exercise_id', 'left')
            ->where('appx_application_information.id', $id)
            ->first();

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Fetch the corresponding applicant data
        $applicant = $this->applicantsModel->find($application['applicant_id']);
        if (!$applicant) {
            // Handle case where applicant record is missing, though unlikely if application exists
            log_message('warning', 'Applicant record not found for application ID: ' . $id . ' and applicant ID: ' . $application['applicant_id']);
            $applicant = []; // Provide an empty array to avoid errors in the view
        }

        // Get related data for this application
        $experiences = $this->experienceModel->getExperiencesByApplicationId($id);
        $education = $this->educationModel->getEducationByApplicationId($id);
        $files = $this->filesModel->getFilesByApplicationId($id);

        // Get position and exercise data
        $position = [
            'id' => $application['position_id'],
            'designation' => $application['designation'] ?? 'N/A'
        ];

        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'] ?? 'N/A'
        ];

        // Parse pre-screening criteria
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            try {
                $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
            } catch (\Exception $e) {
                log_message('error', 'Error parsing pre-screening criteria: ' . $e->getMessage());
            }
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'applicant' => $applicant, // Pass applicant data to the view
            'position' => $position,
            'exercise' => $exercise,
            'preScreenCriteria' => $preScreenCriteria,
            'experiences' => $experiences,
            'education' => $education,
            'files' => $files,
            'educationModel' => $this->educationModel
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_pre_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        // Find the application first to ensure it exists
        $application = $this->applicationModel->find($id);
        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Validate input
        $rules = [
            'status' => 'required|in_list[passed,failed,pending]',
            'remarks' => 'permit_empty|string|max_length[1000]'
        ];

        if ($this->request->getPost('status') === 'failed' && empty($this->request->getPost('remarks'))) {
            $rules['remarks'] = 'required|string|max_length[1000]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Process criteria results
        $criteriaResults = [];
        $criteriaIndices = $this->request->getPost('criteria_index') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        foreach ($criteriaIndices as $index => $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Update the application
        if ($this->applicationModel->update($id, $data)) {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('success', 'Pre-screening results saved successfully.');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to save pre-screening results: ' . implode(', ', $this->applicationModel->errors()));
        }
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * Handles standard form submission, not AJAX.
     * URI: /application_pre_screening/batch_update
     *
     * Expects POST data:
     * - ids[]: Array of application IDs to update
     * - status: The status to apply (e.g., 'passed', 'failed')
     * - remarks: Optional remarks for the batch action
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        // --- Validation ---
        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected for batch update.');
            return redirect()->back(); // Redirect back to the list page
        }

        $allowed_statuses = ['passed', 'failed', 'pending']; // Define allowed statuses
        if (empty($status) || !in_array($status, $allowed_statuses)) {
            $this->session->setFlashdata('error', 'Invalid or missing status for batch update.');
            return redirect()->back()->withInput(); // Keep selected IDs if possible
        }

        // Require remarks if status is 'failed'
        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // --- Prepare Data ---
        // Note: Batch update won't have detailed criteria results.
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks, // Add prefix for clarity
            'pre_screened_criteria_results' => null, // Clear criteria results for batch actions
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        // --- Perform Update ---
        $successCount = 0;
        $failCount = 0;
        $db = \Config\Database::connect(); // Get database connection for batching (optional but can be efficient)

        // Consider wrapping in transaction if atomicity is critical
        // $db->transStart();

        foreach ($ids as $id) {
            $id = (int) $id; // Sanitize ID
            if ($id > 0) {
                try {
                    // Use updateBatch later if needed, for now individual updates are simpler
                    if ($this->applicationModel->update($id, $data)) {
                        $successCount++;
                    } else {
                        log_message('warning', 'Batch pre-screen failed for ID ' . $id . '. Errors: ' . json_encode($this->applicationModel->errors()));
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++; // Invalid ID
            }
        }

        // $db->transComplete();
        // if ($db->transStatus() === false) {
        //     // Handle transaction failure
        //     $this->session->setFlashdata('error', 'Database transaction failed during batch update.');
        //     return redirect()->back();
        // }

        // --- Set Flash Message ---
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully. ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0 && $failCount == 0) {
            $this->session->setFlashdata('success', $message);
        } elseif ($failCount > 0) {
            $this->session->setFlashdata('warning', $message); // Use warning if some failed
        } else {
             $this->session->setFlashdata('info', 'No applications were updated.'); // Should not happen if validation passed
        }

        // Redirect back to the index page (or wherever the batch form was)
        return redirect()->to(base_url('application_pre_screening'));
    }


    // ========================================================================
    // Helper/Listing Methods (Kept as they provide specific views/lists)
    // ========================================================================

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_pre_screening/exercises
     *
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url()); // Redirect to a safe page, e.g., dashboard
        }

        $exercises = $this->exerciseModel->where('org_id', $orgId)
                                         ->where('status', 'selection') // Assuming 'selection' means active for applications
                                         ->orderBy('exercise_name', 'ASC')
                                         ->findAll();

        $data = [
            'title' => 'Pre-Screening Exercises',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_exercise_list', $data);
    }

    /**
     * [GET] Display position groups for a specific exercise.
     * URI: /applications_pre_screening_exercise/view/{exerciseId}
     *
     * @param int $exerciseId The ID of the exercise
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function viewPositionGroups($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('applications_pre_screening_exercise'));
        }

        // Get position groups associated with this exercise
        $positionGroups = $this->positionGroupModel
            ->select('
                positions_groups.*,
                COUNT(DISTINCT positions.id) as positions_count,
                COUNT(appx_application_information.id) as applications_count,
                SUM(CASE WHEN appx_application_information.pre_screened IS NULL OR appx_application_information.pre_screened = \'\' THEN 1 ELSE 0 END) as pending_count
            ')
            ->join('positions', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('appx_application_information', 'appx_application_information.position_id = positions.id AND appx_application_information.recieved_acknowledged IS NOT NULL', 'left')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->groupBy('positions_groups.id')
            ->orderBy('positions_groups.group_name', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Position Groups for Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_pre_screening/application_pre_screening_position_groups', $data);
    }

    /**
     * [GET] Display positions for a specific position group.
     * URI: /applications_pre_screening_exercise/group/{groupId}
     *
     * @param int $groupId The ID of the position group
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function viewPositions($groupId)
    {
        $positionGroup = $this->positionGroupModel
            ->select('positions_groups.*, exercises.exercise_name, exercises.id as exercise_id')
            ->join('exercises', 'exercises.id = positions_groups.exercise_id')
            ->find($groupId);

        if (!$positionGroup) {
            $this->session->setFlashdata('error', 'Position group not found.');
            return redirect()->to(base_url('applications_pre_screening_exercise'));
        }

        // Get positions associated with this group
        $positions = $this->positionModel
            ->select('
                positions.*,
                COUNT(appx_application_information.id) as applications_count,
                SUM(CASE WHEN appx_application_information.pre_screened IS NULL OR appx_application_information.pre_screened = \'\' THEN 1 ELSE 0 END) as pending_count
            ')
            ->join('appx_application_information', 'appx_application_information.position_id = positions.id AND appx_application_information.recieved_acknowledged IS NOT NULL', 'left')
            ->where('positions.position_group_id', $groupId)
            ->groupBy('positions.id')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Positions in Group: ' . esc($positionGroup['group_name']),
            'menu' => 'applications',
            'positionGroup' => $positionGroup,
            'exercise' => [
                'id' => $positionGroup['exercise_id'],
                'exercise_name' => $positionGroup['exercise_name']
            ],
            'positions' => $positions
        ];

        return view('application_pre_screening/application_pre_screening_positions', $data);
    }

    /**
     * [GET] Display positions and application counts for a specific exercise relevant to pre-screening.
     * URI: /application_pre_screening/exercise_applications/{exerciseId}
     *
     * @param int $exerciseId The ID of the exercise
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function exerciseApplications($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('application_pre_screening/exercises'));
        }

        // Get positions associated with this exercise that have applications needing pre-screening
        $positionsWithApplications = $this->applicationModel
            ->select('
                appx_application_information.position_id,
                COUNT(appx_application_information.id) as total_application_count,
                SUM(CASE WHEN appx_application_information.pre_screened IS NULL OR appx_application_information.pre_screened = \'\' THEN 1 ELSE 0 END) as pending_prescreen_count,
                positions.designation as position_name,
                positions.position_group_id,
                positions_groups.group_name as position_group_name
            ')
            ->join('positions', 'positions.id = appx_application_information.position_id')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id') // Use INNER JOIN if position must belong to a group
            ->where('positions_groups.exercise_id', $exerciseId)
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL') // Only acknowledged applications
            ->groupBy('appx_application_information.position_id, positions.designation, positions.position_group_id, positions_groups.group_name')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();


        $data = [
            'title' => 'Exercise Positions for Pre-Screening: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positionsData' => $positionsWithApplications, // Renamed for clarity
        ];

        return view('application_pre_screening/application_pre_screening_exercise_positions', $data);
    }

    /**
     * [GET] Display applications for a specific position that need pre-screening.
     * URI: /application_pre_screening/position_applications/{positionId}
     *
     * @param int $positionId The ID of the position
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function positionApplications($positionId)
    {
        $position = $this->positionModel
                        ->select('positions.*, positions_groups.group_name, positions_groups.id as position_group_id, exercises.exercise_name, exercises.id as exercise_id')
                        ->join('positions_groups', 'positions_groups.id = positions.position_group_id', 'left')
                        ->join('exercises', 'exercises.id = positions_groups.exercise_id', 'left')
                        ->find($positionId);

        if (!$position) {
            $this->session->setFlashdata('error', 'Position not found.');
            // Redirect intelligently: if exercise context exists, go there, else go to general list
            $exerciseId = $this->request->getGet('exercise_id'); // Check if exercise context passed in URL
            if ($exerciseId) {
                 return redirect()->to(base_url('application_pre_screening/exercise_applications/' . $exerciseId));
            }
            return redirect()->to(base_url('application_pre_screening/exercises')); // Fallback
        }

        // Get position group data to fix the undefined variable error in the view
        $positionGroup = null;
        if (!empty($position['position_group_id'])) {
            $positionGroup = [
                'id' => $position['position_group_id'],
                'group_name' => $position['group_name']
            ];
        }

        // Get the complete exercise data to avoid 'undefined array key' errors
        $exercise = null;
        if (!empty($position['exercise_id'])) {
            // Fetch the full exercise record rather than creating a partial one
            $exerciseData = $this->exerciseModel->find($position['exercise_id']);
            if ($exerciseData) {
                $exercise = $exerciseData;
            } else {
                // Fallback to basic info if full record not found
                $exercise = [
                    'id' => $position['exercise_id'],
                    'exercise_name' => $position['exercise_name'],
                    'advertisement_no' => 'N/A'
                ];
            }
        }

        // Get all acknowledged applications for this position, regardless of pre-screening status
        $applications = $this->applicationModel
            ->select('appx_application_information.*, applicants.fname, applicants.lname, applicants.email')
            ->join('applicants', 'applicants.applicant_id = appx_application_information.applicant_id', 'left')
            ->where('appx_application_information.position_id', $positionId)
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL') // Show only acknowledged applications
            ->orderBy('appx_application_information.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Applications for Position: ' . esc($position['designation']),
            'menu' => 'applications',
            'position' => $position,
            'positionGroup' => $positionGroup, // Add position group data to the view
            'exercise' => $exercise, // Now contains complete exercise data
            'applications' => $applications
        ];

        return view('application_pre_screening/application_pre_screening_position_applications', $data);
    }

    /**
     * [GET] Display all applications that have been acknowledged (regardless of pre-screening status).
     * URI: /application_pre_screening/all_acknowledged
     */
    public function allAcknowledgedApplications()
    {
        // Dummy data for UI development
        $applications = [
            [
                'id' => 1,
                'applicant_id' => 101,
                'fname' => 'John',
                'lname' => 'Doe',
                'position_name' => 'Senior Software Engineer',
                'application_number' => 'APP-2024-001',
                'recieved_acknowledged' => '2024-01-15 10:30:00',
                'pre_screened_status' => 'passed',
                'prescreened_by_user' => 'admin',
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'applicant_id' => 102,
                'fname' => 'Jane',
                'lname' => 'Smith',
                'position_name' => 'Project Manager',
                'application_number' => 'APP-2024-002',
                'recieved_acknowledged' => '2024-01-14 14:20:00',
                'pre_screened_status' => null,
                'prescreened_by_user' => null,
                'created_at' => '2024-01-09 11:15:00'
            ]
        ];

        $data = [
            'title' => 'All Acknowledged Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        // Assuming you have a view file specifically for this list
        return view('application_pre_screening/all_acknowledged_applications_list', $data);
    }

    /**
     * [GET] Display detailed view of an application for pre-screening
     * URI: /application_pre_screening/viewApplication/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function viewApplication($id)
    {
        // Redirect to the show method which already handles detailed view
        return redirect()->to(base_url('application_pre_screening/show/' . $id));
    }

}
