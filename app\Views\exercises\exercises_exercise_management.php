<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Exercise Management</h5>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exerciseModal">
                        <i class="fas fa-plus me-2"></i>New Exercise
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="exerciseTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Exercise Name</th>
                            <th>Gazzetted No</th>
                            <th>Advertisement No</th>
                            <th>Publish Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Exercise Modal -->
<div class="modal fade" id="exerciseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <?= form_open('exercises/create', ['id' => 'exerciseForm']) ?>
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-file-alt me-2"></i>Exercise Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="id" id="exercise_id">
                    <input type="hidden" name="org_id" value="<?= session()->get('org_id') ?>">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Exercise Name</label>
                            <input type="text" class="form-control" name="exercise_name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Gazzetted No</label>
                            <input type="text" class="form-control" name="gazzetted_no">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Gazzetted Date</label>
                            <input type="date" class="form-control" name="gazzetted_date">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Advertisement No</label>
                            <input type="text" class="form-control" name="advertisement_no">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Advertisement Date</label>
                            <input type="date" class="form-control" name="advertisement_date">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Mode of Advertisement</label>
                            <input type="text" class="form-control" name="mode_of_advertisement"
                                   placeholder="e.g., Newspaper, Online, TV">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Publish Date From</label>
                            <input type="date" class="form-control" name="publish_date_from">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Publish Date To</label>
                            <input type="date" class="form-control" name="publish_date_to">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary" id="btnSaveExercise">
                        <i class="fas fa-save me-2"></i>Save Exercise
                    </button>
                </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Add the status change modal at the end of the page -->
<div class="modal fade" id="changeStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt me-2"></i> Change Exercise Status
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="changeStatusForm" action="<?= base_url('exercises/change-status') ?>" method="post">
                <!-- CSRF token -->
                <?= csrf_field() ?>

                <div class="modal-body p-4">
                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-light mb-3">
                            <i class="fas fa-clipboard-list text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <h5 id="exercise_name"></h5>
                        <p class="text-muted">Current Status: <span class="badge bg-secondary" id="current_status">Draft</span></p>
                    </div>

                    <input type="hidden" id="status_exercise_id" name="id">

                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status</label>
                        <select class="form-select form-select-lg" id="new_status" name="status" required>
                            <option value="draft">Draft</option>
                            <option value="publish_request">Publish Request</option>
                            <option value="publish">Publish</option>
                            <option value="selection">Selection</option>
                            <option value="review">Review</option>
                            <option value="closed">Closed</option>
                        </select>
                        <div class="form-text text-muted mt-2">
                            <ul class="ps-3 mb-0">
                                <li><b>Draft</b>: Work in progress, not visible to public</li>
                                <li><b>Publish Request</b>: Pending approval for publication</li>
                                <li><b>Publish</b>: Publicly visible exercise</li>
                                <li><b>Selection</b>: In selection phase</li>
                                <li><b>Review</b>: Under review</li>
                                <li><b>Closed</b>: Exercise is closed and archived</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Your custom script -->
<script>
// Debug helper function - only logs in development environment
function debug(label, data) {
    // Uncomment the line below if you need debugging
    // console.log(`DEBUG [${label}]:`, data);
}

document.addEventListener('DOMContentLoaded', function() {
    if (typeof jQuery != 'undefined') {
        initializeExerciseManagement();
    } else {
        console.error('jQuery is not loaded');
    }
});

function initializeExerciseManagement() {
    // Initialize DataTable
    const table = $('#exerciseTable').DataTable({
        ajax: {
            url: '<?= base_url('exercises/list') ?>',
            dataSrc: function(json) {
                // Debug call removed
                return json.data;
            }
        },
        columns: [
            { data: 'exercise_name' },
            { data: 'gazzetted_no' },
            { data: 'advertisement_no' },
            { data: 'publish_date_from' },
            {
                data: 'status',
                render: function(data) {
                    // Updated status names and colors to match Dakoii implementation
                    const statusClasses = {
                        'draft': 'bg-secondary',
                        'publish': 'bg-success',
                        'publish_request': 'bg-warning',
                        'selection': 'bg-info',
                        'review': 'bg-primary',
                        'closed': 'bg-danger'
                    };

                    // Determine text color based on background
                    const textColor = (data === 'publish_request' || data === 'warning') ? 'text-dark' : 'text-white';

                    // Format the status display with proper capitalization and spacing
                    const displayStatus = data.replace('_', ' ');

                    // Improved contrast with solid colors and proper text color
                    return `<span class="badge ${statusClasses[data] || 'bg-secondary'} ${textColor}" style="font-weight: 500; padding: 0.35em 0.65em;">${displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}</span>`;
                }
            },
            {
                data: null,
                render: function(data) {
                    const editButton = `
                        <button class="btn btn-sm btn-primary edit-btn" data-id="${data.id}">
                            <i class="fas fa-edit"></i>
                        </button>`;

                    // Only show delete button for exercises in draft status
                    let deleteButton = '';
                    if (data.status === 'draft') {
                        deleteButton = `
                        <button class="btn btn-sm btn-danger delete-btn" data-id="${data.id}">
                            <i class="fas fa-trash"></i>
                        </button>`;
                    }

                    // Add status change button for all statuses
                    const statusButton = `
                    <button class="btn btn-sm btn-info change-status-btn" data-id="${data.id}" data-name="${data.exercise_name}" data-status="${data.status}">
                        <i class="fas fa-exchange-alt"></i>
                    </button>`;

                    // Add pre-screening criteria button
                    const criteriaButton = `
                    <a href="<?= base_url('exercises/pre_screen_criteria') ?>/${data.id}" class="btn btn-sm btn-warning" title="Pre-Screening Criteria">
                        <i class="fas fa-filter"></i>
                    </a>`;

                    return `<div class="btn-group">${editButton}${statusButton}${criteriaButton}${deleteButton}</div>`;
                }
            }
        ]
    });

    // Form Submit Handler
    $('#exerciseForm').on('submit', function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $.ajax({
            type: 'POST',
            url: $(this).attr('action'),
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#exerciseModal').modal('hide');
                    table.ajax.reload();
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('Failed to save exercise');
            }
        });
    });

    // Edit Button Handler
    $('#exerciseTable').on('click', '.edit-btn', function() {
        const id = $(this).data('id');

        // Get the exercise data
        $.get(`<?= base_url('exercises/get') ?>/${id}`, function(response) {
            if (response) {
                // Fill the form with exercise data
                $('#exerciseForm')[0].reset();
                $('#exercise_id').val(response.id);

                // Fill in all the fields
                for (const key in response) {
                    const input = $(`[name="${key}"]`);
                    if (input.length) {
                        input.val(response[key]);
                    }
                }

                // Change form action to update
                $('#exerciseForm').attr('action', `<?= base_url('exercises/update') ?>/${id}`);

                // Open the modal
                $('#exerciseModal').modal('show');
            }
        }).fail(function() {
            toastr.error('Failed to load exercise data');
        });
    });

    // Delete Button Handler
    $('#exerciseTable').on('click', '.delete-btn', function() {
        const id = $(this).data('id');
        const row = $(this).closest('tr');
        const rowData = table.row(row).data();

        // Double-check that status is draft before allowing deletion
        if (rowData.status !== 'draft') {
            toastr.error('Only exercises in draft status can be deleted');
            return;
        }

        if (confirm('Are you sure you want to delete this exercise?')) {
            $.post(`<?= base_url('exercises/delete') ?>/${id}`, function(response) {
                if (response.success) {
                    table.ajax.reload();
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            }).fail(function() {
                toastr.error('Failed to delete exercise');
            });
        }
    });

    // Modal reset handlers
    $('#exerciseModal').on('hidden.bs.modal', function() {
        $('#exerciseForm')[0].reset();
        $('#exercise_id').val('');
    });

    // Reset status change modal when closed
    $('#changeStatusModal').on('hidden.bs.modal', function() {
        $('#changeStatusForm')[0].reset();
        $('#status_exercise_id').val('');
        $('#exercise_name').text('');
        $('#current_status').removeClass().addClass('badge bg-secondary').text('Draft');
    });

    // Add keypress event listener for form inputs
    $('#exerciseForm input').keypress(function(e) {
        if (e.which == 13) {
            e.preventDefault();
            $('#btnSaveExercise').click();
        }
    });

    // Add event handler for status change button
    $('#exerciseTable').on('click', '.change-status-btn', function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        const currentStatus = $(this).data('status');

        // Verify that we have a valid ID
        if (!id) {
            toastr.error('Cannot change status: Invalid exercise ID');
            return;
        }

        // Open status change modal
        $('#changeStatusModal').modal('show');

        // Set the ID
        $('#status_exercise_id').val(id);
        $('#exercise_name').text(name);

        // Set appropriate badge class and format text
        const statusClasses = {
            'draft': 'bg-secondary',
            'publish': 'bg-success',
            'publish_request': 'bg-warning',
            'selection': 'bg-info',
            'review': 'bg-primary',
            'closed': 'bg-danger'
        };

        const textColor = (currentStatus === 'publish_request') ? 'text-dark' : 'text-white';
        const displayStatus = currentStatus.replace('_', ' ');
        const formattedStatus = displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1);

        $('#current_status')
            .removeClass()
            .addClass(`badge ${statusClasses[currentStatus] || 'bg-secondary'} ${textColor}`)
            .text(formattedStatus);

        // Set current status in dropdown
        $('#new_status').val(currentStatus);
    });

    // Initialize status change form submission
    $('#changeStatusForm').on('submit', function(e) {
        e.preventDefault();

        // Check if ID is present
        const exerciseId = $('#status_exercise_id').val();
        const newStatus = $('#new_status').val();

        if (!exerciseId || exerciseId === '') {
            toastr.error('Cannot update status: Missing exercise ID');
            return;
        }

        $.ajax({
            type: 'POST',
            url: $(this).attr('action'),
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success notification
                    toastr.success(response.message);

                    // Close the modal
                    $('#changeStatusModal').modal('hide');

                    // Refresh the table
                    $('#exerciseTable').DataTable().ajax.reload();
                } else {
                    // Show error notification
                    toastr.error(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Status change error:', error);
                toastr.error('An error occurred while updating the status.');
            }
        });
    });
}
</script>
<?= $this->endSection() ?>